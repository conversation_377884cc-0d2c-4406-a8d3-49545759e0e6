import { WorkerEntrypoint } from 'cloudflare:workers';
import temp from './templates/signed-funding-application.html';

import { PDF } from './classes/pdf';
import { Storage } from './classes/storage';
import { Template } from './classes/template';

export default class extends WorkerEntrypoint {
  async fetch(request) {
    try {
      const env = this.env;

      const { pathname } = new URL(request.url);

      if (pathname.startsWith('/doc/')) {
        const name = pathname.split('/').pop();

        const result = await Storage.get({ name, env });
        const pdf = await result.arrayBuffer();

        return new Response(pdf, {
          headers: {
            'Content-Type': 'application/pdf',
          },
        });
      }

      return new Response('Not Found', { status: 404 });
    } catch (e) {
      console.error(e);
      return new Response('Internal Server Error', { status: 500 });
    }
  }

  async signDocument({ template, data }) {
    const env = this.env;

    const html = Template.fill({
      template,
      data,
      env,
    });

    const pdf = await PDF.generate({
      html,
      env,
    });

    const uuid = data.uuid;

    await Promise.all([
      Storage.savePdf({ name: `signed-application-${uuid}`, content: pdf, env }),
      Storage.saveImage({ name: `signature-${uuid}`, content: data.applicationFields.signature, env }),
    ]);

    return {
      pdfFilename: `signed-application-${uuid}.pdf`,
      signatureFilename: `signature-${uuid}.png`,
    };
  }
}
