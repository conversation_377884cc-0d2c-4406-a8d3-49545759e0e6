export class Storage {
  static async save({ name, content, options, env }) {
    const result = await env.R2.put(name, content, options);
    console.log(`Saved ${name}.`);
    return result;
  }

  static async savePdf({ name, content, env }) {
    const options = {
      httpMetadata: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${name}"`,
      },
    };
    return this.save({ name: `${name}.pdf`, content, options, env });
  }

  static async saveImage({ name, content, env }) {
    const options = {
      httpMetadata: {
        'Content-Type': 'image/png',
        'Content-Disposition': `attachment; filename="${name}"`,
      },
    };
    return this.save({ name: `${name}.png`, content, options, env });
  }

  static async get({ name, env }) {
    const object = await env.R2.get(name);
    return object;
  }
}
