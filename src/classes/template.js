
export class Template {
  static fill({ template, data, env }) {
    if (!env) throw new Error('missing env');
    console.log(`Generating template: ${template} with data:`, data);

    switch (template) {
      case 'signed-funding-application':
        return this.generateSignedFundingApplication(data);
      default:
        throw new Error(`Unknown template: ${template}`);
    }
  }

  static generateSignedFundingApplication(data) {
    const signedAt = data.signed_at.slice(0, 10);
    const { uuid, applicationFields } = data;
    const {
      businessName,
      dbaName,
      website,
      entityType,
      ein,
      industry,
      businessStartDate,
      businessPhone,
      businessEmail,
      address,
      owners,
      signature,
    } = applicationFields;
  }
}
