import puppeteer from '@cloudflare/puppeteer';

export class PDF {
  static async generate({ html, env }) {
    if (!env) throw new Error('missing env');

    const browser = await puppeteer.launch(env.BROWSER);
    const page = await browser.newPage();
    await page.setContent(html);
    const pdf = await page.pdf({ printBackground: true, margin: { top: 0, right: 0, bottom: 0, left: 0 } });
    await browser.close();

    return pdf;
  }
}
